"use client"

import { useState, useTransition } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { LinkButton } from "@/components/ui/link-button"

export default function RegisterPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [name, setName] = useState("")
  const [role, setRole] = useState<"subscriber" | "writer">("subscriber")
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [isPending, startTransition] = useTransition()
  const router = useRouter()
  const supabase = createSupabaseClient()

  const handleRegister = (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setSuccess("")

    startTransition(async () => {
      try {
        // Sign up with Supabase Auth
        const { data, error: signUpError } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              name: name,
              role: role
            }
          }
        })

        if (signUpError) {
          setError(signUpError.message)
          return
        }

        if (data.user) {
          setSuccess("Account created successfully! Please check your email to verify your account.")
          setTimeout(() => router.push("/login"), 3000)
        }
      } catch {
        setError("An unexpected error occurred")
      }
    })
  }

  return (
    <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center px-6">
      <div className="max-w-md w-full bg-white rounded-lg p-8 shadow-sm">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-serif text-gray-800">Create Account</h1>
          <p className="text-gray-600 font-serif mt-2">
            Create an account to read and write personal stories
          </p>
        </div>

        <form onSubmit={handleRegister} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Full Name
            </label>
            <input
              id="name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
              placeholder="Your full name"
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              minLength={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
              placeholder="At least 6 characters"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Account Type
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="subscriber"
                  checked={role === "subscriber"}
                  onChange={(e) => setRole(e.target.value as "subscriber")}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">
                  Reader - Subscribe to creators and read their stories
                </span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="writer"
                  checked={role === "writer"}
                  onChange={(e) => setRole(e.target.value as "writer")}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">
                  Writer - Share your stories and build an audience
                </span>
              </label>
            </div>
          </div>

          {error && (
            <div className="text-red-600 text-sm font-medium bg-red-50 p-3 rounded-lg">
              {error}
            </div>
          )}

          {success && (
            <div className="text-green-600 text-sm font-medium bg-green-50 p-3 rounded-lg">
              {success}
            </div>
          )}

          <Button
            type="submit"
            isLoading={isPending}
            className="w-full bg-gray-800 text-white hover:bg-gray-700"
          >
            Create Account
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-gray-600 font-serif mb-4">
            Already have an account?
          </p>
          <LinkButton href="/login" variant="outline" className="w-full">
            Sign In
          </LinkButton>
        </div>
      </div>
    </div>
  )
}