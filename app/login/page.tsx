"use client"

import { useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import Link from "next/link"
import { LinkButton } from "@/components/ui/link-button"
import { useRouter } from "next/navigation" // Import useRouter

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")

  const supabase = createSupabaseClient()
  const router = useRouter() // Initialize useRouter

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setLoading(true)

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim(),
        password: password.trim(),
      })

      if (error) {
        setError(error.message || "Login failed. Please try again.")
        setLoading(false)
        return
      }

      if (data?.session?.user) {
        // Get user profile to determine redirect destination
        const { data: profile } = await supabase
          .from('users')
          .select('role')
          .eq('id', data.session.user.id)
          .single()
        
        // Add a small delay before redirecting
        await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay

        if (profile?.role === 'subscriber') {
          setLoading(false) // Ensure loading is false before redirect
          router.replace('/timeline') // Use router.replace
        } else if (profile?.role === 'writer') {
          setLoading(false) // Ensure loading is false before redirect
          router.replace('/dashboard') // Use router.replace
        } else {
          // For visitors or undefined roles, go to home page
          setLoading(false) // Ensure loading is false before redirect
          router.replace('/') // Use router.replace
        }
      } else {
        setError("Login failed. Please try again.")
        setLoading(false)
      }
    } catch {
      setError("An unexpected error occurred. Please try again.")
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center px-6">
      <div className="max-w-md w-full bg-white rounded-lg p-8 shadow-sm">
        <div className="text-center mb-8">
          <Link href="/" className="text-2xl font-serif text-gray-800 hover:text-gray-600">
            OnlyDiary
          </Link>
          <h1 className="text-2xl font-serif mt-4 text-gray-800">Welcome Back</h1>
          <p className="text-gray-600 font-serif mt-2">
            Sign in to your account to access your subscriptions
          </p>
        </div>

        <form onSubmit={handleLogin} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
              placeholder="Your password"
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm font-medium bg-red-50 p-3 rounded-lg">
              {error}
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-gray-800 text-white hover:bg-gray-700 px-4 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Signing In...
              </div>
            ) : (
              'Sign In'
            )}
          </button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-gray-600 font-serif mb-4">
            Don&apos;t have an account?
          </p>
          <LinkButton href="/register" variant="outline" className="w-full">
            Create Account
          </LinkButton>
        </div>

        <div className="mt-6 text-center">
          <LinkButton href="/forgot-password" variant="ghost" className="text-gray-600 hover:text-gray-800 text-sm p-0 h-auto">
            Forgot your password?
          </LinkButton>
        </div>
      </div>
    </div>
  )
}
