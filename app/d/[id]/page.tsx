import { createSupabaseServerClient } from "@/lib/supabase/client"
import { notFound } from "next/navigation"
import Link from "next/link"
import { DiaryInteractions } from "@/components/DiaryInteractions"
import { CommentsSection } from "@/components/CommentsSection"
// import { SubscriptionButtons } from "@/components/SubscriptionButtons"
import { LinkButton } from "@/components/ui/link-button"
import { LoveButton } from "@/components/LoveButton"
import { PhotoGallery } from "@/components/PhotoGallery"
// import { SmartTypographyWrapper } from "@/components/SmartTypographyWrapper"
import { StructuredData } from "@/components/StructuredData"
import { ImmersiveReader } from "@/components/ImmersiveReader"

interface DiaryPageProps {
  params: Promise<{
    id: string
  }>
}

// function formatPrice(cents: number) {
//   return `$${(cents / 100).toFixed(2)}`
// }

// function formatDate(dateString: string) {
//   return new Date(dateString).toLocaleDateString('en-US', {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric'
//   })
// }

// function formatDateTime(dateString: string) {
//   return new Date(dateString).toLocaleString('en-US', {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric',
//     hour: 'numeric',
//     minute: '2-digit',
//     hour12: true
//   })
// }

// function getTeaserText(bodyMd: string, maxLength: number = 120) {
//   const plainText = bodyMd.replace(/[#*`_~\[\]()]/g, '').trim()
//   if (plainText.length <= maxLength) return plainText
//   return plainText.slice(0, maxLength) + '...'
// }

export default async function DiaryPage({ params }: DiaryPageProps) {
  const { id } = await params
  const supabase = await createSupabaseServerClient()
  
  // Get the current user (if any)
  const { data: { user } } = await supabase.auth.getUser()
  
  // First try to get the entry using the RPC (for published entries)
  let { data: entryData } = await supabase
    .rpc('get_entry_preview', {
      entry_id: id,
      viewer_id: user?.id
    })

  // If no data found and user is logged in, check if it's their own hidden entry
  if ((!entryData || entryData.length === 0) && user) {
    const { data: hiddenEntry, error: hiddenError } = await supabase
      .from('diary_entries')
      .select(`
        id,
        title,
        body_md,
        is_free,
        is_hidden,
        created_at,
        updated_at,
        user_id,
        users!inner (
          id,
          name,
          custom_url,
          price_monthly
        )
      `)
      .eq('id', id)
      .eq('user_id', user.id)
      .eq('is_hidden', true)
      .single()

    if (hiddenEntry && !hiddenError) {
      // Transform to match the RPC response format
      entryData = [{
        id: hiddenEntry.id,
        title: hiddenEntry.title,
        body_md: hiddenEntry.body_md,
        is_free: hiddenEntry.is_free,
        is_hidden: hiddenEntry.is_hidden,
        created_at: hiddenEntry.created_at,
        updated_at: hiddenEntry.updated_at,
        user_id: hiddenEntry.user_id,
        writer_id: hiddenEntry.user_id,
        writer_name: hiddenEntry.users[0].name,
        writer_custom_url: hiddenEntry.users[0].custom_url,
        writer_price: hiddenEntry.users[0].price_monthly || 999,
        can_read_full: true // Writer can always read their own content
      }]
    }
  }

  if (!entryData || entryData.length === 0) {
    notFound()
  }
  
  const entry = entryData[0]
  const canReadFull = entry.can_read_full || entry.is_free
  const isTeaser = !canReadFull

  // Get photos for this entry
  const { data: photos } = await supabase
    .from('photos')
    .select('*')
    .eq('diary_entry_id', id)
    .eq('moderation_status', 'approved')
    .order('created_at', { ascending: true })
  
  return (
    <>
      <StructuredData
        type="article"
        data={{
          id: entry.id,
          title: entry.title,
          content: entry.body_md,
          created_at: entry.created_at,
          updated_at: entry.updated_at,
          author: {
            id: entry.writer_id,
            name: entry.writer_name,
            custom_url: entry.writer_custom_url
          },
          photos: photos || undefined
        }}
      />
      <ImmersiveReader
        entry={entry}
        user={user ? { id: user.id, role: user.role ?? '' } : null}
        canReadFull={canReadFull}
      >
        {/* Photos - Facebook style display */}
        {canReadFull && (
          <PhotoGallery photos={photos || []} />
        )}

        {/* Enhanced Paywall for locked content */}
        {isTeaser && (
          <div className="mt-8 p-6 sm:p-8 bg-gradient-to-br from-purple-50 to-blue-50 rounded-2xl border border-purple-200 shadow-lg">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">🔓</span>
              </div>
              <h3 className="text-xl sm:text-2xl font-serif mb-4 text-gray-800">
                Continue Reading
              </h3>
              <p className="text-gray-600 font-serif mb-6 text-sm sm:text-base">
                Subscribe to {entry.writer_name} to read this entry and all their future posts.
              </p>
              <div className="space-y-3">
                <Link
                  href={`/u/${entry.writer_id}`}
                  className="block w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center"
                >
                  Subscribe to {entry.writer_name}
                </Link>
                <Link
                  href="/discover"
                  className="block w-full bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors text-center"
                >
                  Discover Other Creators
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* Love Button */}
        {canReadFull && (
          <div className="mb-6">
            <LoveButton
              entryId={entry.id}
              initialLoveCount={entry.love_count || 0}
              userId={user?.id}
            />
          </div>
        )}

        {/* Interactive Elements */}
        <DiaryInteractions
          canReadFull={canReadFull}
          entryId={entry.id}
          writerId={entry.writer_id}
        />

        {/* Comments Section */}
        {canReadFull && (
          <CommentsSection
            entryId={entry.id}
            canComment={canReadFull && !!user}
            userId={user?.id}
          />
        )}

        {/* Login Prompt for Visitors */}
        {!user && (
          <div className="bg-white rounded-lg p-6 shadow-sm text-center">
            <p className="text-gray-600 font-serif mb-4">
              Sign in or create an account to subscribe and unlock all content
            </p>
            <div className="flex gap-3 justify-center">
              <LinkButton href="/login" variant="outline">
                Sign In
              </LinkButton>
              <LinkButton href="/register" className="bg-gray-800 text-white hover:bg-gray-700">
                Create Account
              </LinkButton>
            </div>
          </div>
        )}
      </ImmersiveReader>
    </>
  )
}
