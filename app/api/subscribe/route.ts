import { createSupabaseServerClient } from "@/lib/supabase/client"
import { NextRequest, NextResponse } from "next/server"
import { stripe, calculatePlatformFee } from "@/lib/stripe"

export async function POST(request: NextRequest) {
  try {
    if (!stripe) {
      return NextResponse.json(
        { error: "Stripe is not configured" },
        { status: 503 }
      )
    }

    const { writerId } = await request.json()
    
    if (!writerId) {
      return NextResponse.json(
        { error: "Writer ID is required" },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get writer information
    const { data: writer, error: writerError } = await supabase
      .from("users")
      .select("id, name, price_monthly, stripe_account_id")
      .eq("id", writerId)
      .eq("role", "writer")
      .single()

    if (writerError || !writer) {
      return NextResponse.json(
        { error: "Writer not found" },
        { status: 404 }
      )
    }

    if (!writer.price_monthly) {
      return NextResponse.json(
        { error: "Writer has not set subscription pricing" },
        { status: 400 }
      )
    }

    // Check if user already has an active subscription
    const { data: existingSubscription } = await supabase
      .from("subscriptions")
      .select("id, active_until")
      .eq("subscriber_id", user.id)
      .eq("writer_id", writerId)
      .gte("active_until", new Date().toISOString())
      .single()

    if (existingSubscription) {
      return NextResponse.json(
        { error: "You already have an active subscription to this writer" },
        { status: 400 }
      )
    }

    // Create Stripe Checkout Session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      mode: "subscription",
      customer_email: user.email,
      line_items: [
        {
          price_data: {
            currency: "usd",
            product_data: {
              name: `Subscription to ${writer.name || "Writer"}`,
              description: `Monthly subscription to access all diary entries`,
            },
            unit_amount: writer.price_monthly,
            recurring: {
              interval: "month",
            },
          },
          quantity: 1,
        },
      ],
      metadata: {
        subscriber_id: user.id,
        writer_id: writerId,
        type: "subscription",
      },
      success_url: `${request.nextUrl.origin}/u/${writerId}?subscribed=true`,
      cancel_url: `${request.nextUrl.origin}/u/${writerId}?cancelled=true`,
      // TODO: Add application fee for platform cut when Stripe Connect is set up
      // application_fee_amount: Math.floor(writer.price_monthly * 0.2), // 20% platform fee
    })

    return NextResponse.json({ url: session.url })

  } catch (error) {
    console.error("Error creating subscription:", error)
    return NextResponse.json(
      { error: "Failed to create subscription" },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'OnlyDiary Subscription API',
    version: '1.0.0',
    endpoints: {
      POST: 'Create new subscription with Stripe',
    }
  })
}