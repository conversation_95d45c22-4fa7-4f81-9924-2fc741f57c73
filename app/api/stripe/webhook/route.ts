import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import Stripe from 'stripe'
import { SupabaseClient } from '@supabase/supabase-js'

// This should be set in your environment variables
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET

export async function POST(request: NextRequest) {
  const body = await request.text()
  const signature = request.headers.get('stripe-signature')

  if (!signature || !webhookSecret) {
    return NextResponse.json({ error: 'Missing signature or webhook secret' }, { status: 400 })
  }

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
  } catch (error) {
    console.error('Webhook signature verification failed:', error)
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
  }

  const supabase = await createSupabaseServerClient()

  try {
    switch (event.type) {
      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        await handlePaymentSuccess(paymentIntent, supabase)
        break
      }

      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        await handlePaymentFailure(paymentIntent, supabase)
        break
      }

      case 'account.updated': {
        const account = event.data.object as Stripe.Account
        await handleAccountUpdate(account, supabase)
        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Webhook handler error:', error)
    return NextResponse.json({ error: 'Webhook handler failed' }, { status: 500 })
  }
}

async function handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent, supabase: SupabaseClient) {
  const metadata = paymentIntent.metadata
  const payerId = metadata.payer_id
  const writerId = metadata.writer_id
  const paymentType = metadata.payment_type
  const platformFee = parseInt(metadata.platform_fee)
  const writerAmount = parseInt(metadata.writer_amount)

  // Record payment in database
  const { data: payment, error: paymentError } = await supabase
    .from('payments')
    .insert({
      stripe_payment_intent_id: paymentIntent.id,
      payer_id: payerId,
      writer_id: writerId,
      amount_total: paymentIntent.amount,
      amount_writer: writerAmount,
      amount_platform: platformFee,
      payment_type: paymentType,
      status: 'succeeded',
      metadata: metadata
    })
    .select()
    .single()

  if (paymentError) {
    console.error('Error recording payment:', paymentError)
    return
  }

  if (paymentType === 'subscription') {
    // Grant 30 post credits
    const expiresAt = new Date()
    expiresAt.setMonth(expiresAt.getMonth() + 6) // Credits expire in 6 months

    await supabase
      .from('post_credits')
      .upsert({
        user_id: payerId,
        writer_id: writerId,
        credits_remaining: 30,
        credits_total: 30,
        payment_id: payment.id,
        expires_at: expiresAt.toISOString()
      })
  } else if (paymentType === 'donation') {
    // Record donation
    await supabase
      .from('donations')
      .insert({
        donor_id: payerId,
        writer_id: writerId,
        payment_id: payment.id,
        amount: paymentIntent.amount,
        message: metadata.donation_message || null,
        is_anonymous: false
      })
  }
}

async function handlePaymentFailure(paymentIntent: Stripe.PaymentIntent, supabase: SupabaseClient) {
  const metadata = paymentIntent.metadata

  // Record failed payment
  await supabase
    .from('payments')
    .insert({
      stripe_payment_intent_id: paymentIntent.id,
      payer_id: metadata.payer_id,
      writer_id: metadata.writer_id,
      amount_total: paymentIntent.amount,
      amount_writer: parseInt(metadata.writer_amount),
      amount_platform: parseInt(metadata.platform_fee),
      payment_type: metadata.payment_type,
      status: 'failed',
      metadata: metadata
    })
}

async function handleAccountUpdate(account: Stripe.Account, supabase: SupabaseClient) {
  const userId = account.metadata?.user_id
  
  if (!userId) return

  // Update user's Stripe onboarding status
  const isComplete = account.details_submitted && 
                    account.charges_enabled && 
                    account.payouts_enabled

  await supabase
    .from('users')
    .update({
      stripe_onboarding_complete: isComplete
    })
    .eq('stripe_account_id', account.id)
}
