import { createSupabaseServerClient } from "@/lib/supabase/client"
import { redirect } from "next/navigation"
import Link from "next/link"
import { WithdrawalSection } from "@/components/WithdrawalSection"
// import { LinkButton } from "@/components/ui/link-button"
import { StripeConnectButton } from "@/components/StripeConnectButton"
import { EntriesManager } from "@/components/EntriesManager"
import { InvitePrompt } from "@/components/InvitePrompt"

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

// function formatDate(dateString: string) {
//   return new Date(dateString).toLocaleDateString('en-US', {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric'
//   })
// }

export default async function WriterDashboard() {
  const supabase = await createSupabaseServerClient()
  
  // Check if user is authenticated
  const { data: { user }, error: authError } = await supabase.auth.getUser()
  
  if (authError || !user) {
    redirect('/login')
  }

  // Get user profile
  const { data: profile, error: profileError } = await supabase
    .from("users")
    .select("*")
    .eq("id", user.id)
    .single()

  if (profileError || !profile) {
    redirect('/')
  }

  // Redirect subscribers to their timeline
  if (profile.role === 'subscriber') {
    redirect('/timeline')
  }

  // Only writers can access this dashboard
  if (profile.role !== 'writer') {
    redirect('/')
  }

  // Get writer's entries
  const { data: entries } = await supabase
    .from("diary_entries")
    .select("id, title, body_md, created_at, is_free, is_hidden, view_count")
    .eq("user_id", user.id)
    .order("created_at", { ascending: false })

  // Get subscription count
  const { data: subscriptions } = await supabase
    .from("subscriptions")
    .select("id, subscriber_id, active_until")
    .eq("writer_id", user.id)
    .gte("active_until", new Date().toISOString())

  // Get payments for earnings calculation
  const { data: payments } = await supabase
    .from("payments")
    .select("amount_cents, kind")
    .eq("writer_id", user.id)

  // Get withdrawals for balance calculation
  const { data: withdrawals } = await supabase
    .from("withdrawals")
    .select("amount_cents, status")
    .eq("writer_id", user.id)
    .neq("status", "rejected")

  const activeSubscriberCount = subscriptions?.length || 0
  const monthlyRevenue = activeSubscriberCount * (profile.price_monthly || 0)
  const totalEntries = entries?.length || 0
  const totalViews = entries?.reduce((sum, entry) => sum + (entry.view_count || 0), 0) || 0


  // Calculate earnings and available balance
  const totalEarnings = payments?.reduce((sum, p) => sum + p.amount_cents, 0) || 0
  const totalWithdrawals = withdrawals?.reduce((sum, w) => sum + w.amount_cents, 0) || 0

  // Calculate platform fees based on payment type (5% for donations, 20% for subscriptions)
  const totalPlatformFees = payments?.reduce((sum, p) => {
    const feeRate = p.kind === 'donation' ? 0.05 : 0.20
    return sum + Math.floor(p.amount_cents * feeRate)
  }, 0) || 0

  const availableBalance = totalEarnings - totalPlatformFees - totalWithdrawals

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        {/* Clean Header */}
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-serif text-gray-900 mb-2">
            Welcome back, {profile.name || 'Writer'}
          </h1>
          <p className="text-gray-600 font-serif text-sm sm:text-base">
            Manage your writing and track your progress
          </p>
        </div>

        {/* Mobile-Optimized Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-5 gap-3 sm:gap-4 mb-6 sm:mb-8">
          <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-600 mb-1">Subscribers</h3>
            <p className="text-xl sm:text-2xl font-bold text-gray-900">{activeSubscriberCount}</p>
          </div>

          <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-600 mb-1">Revenue</h3>
            <p className="text-xl sm:text-2xl font-bold text-gray-900">{formatPrice(monthlyRevenue)}</p>
          </div>

          <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-600 mb-1">Entries</h3>
            <p className="text-xl sm:text-2xl font-bold text-gray-900">{totalEntries}</p>
          </div>

          <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-600 mb-1">Total Views</h3>
            <p className="text-xl sm:text-2xl font-bold text-gray-900">{totalViews.toLocaleString()}</p>
          </div>

          <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-600 mb-1">Balance</h3>
            <p className="text-xl sm:text-2xl font-bold text-gray-900">{formatPrice(availableBalance)}</p>
            {availableBalance >= 1000 && (
              <button
                onClick={() => document.getElementById('withdrawal-section')?.scrollIntoView({ behavior: 'smooth' })}
                className="text-xs text-gray-600 hover:text-gray-800 mt-1 font-medium"
              >
                Withdraw →
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mb-6 sm:mb-8">
          {/* Profile Summary */}
          <div className="bg-white rounded-lg p-4 sm:p-5 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-serif text-gray-800">Profile</h2>
              <Link
                href="/profile"
                className="text-sm text-gray-600 hover:text-gray-800 font-medium"
              >
                Edit →
              </Link>
            </div>

            <div className="space-y-3 sm:space-y-4">
              <div>
                <label className="text-xs sm:text-sm font-medium text-gray-500">Monthly Subscription Price</label>
                <p className="text-base sm:text-lg font-medium text-gray-900">
                  {profile.price_monthly ? formatPrice(profile.price_monthly) : 'Not set'}
                </p>
              </div>

              <div>
                <label className="text-xs sm:text-sm font-medium text-gray-500">Payment Processing</label>
                <div className="mt-2">
                  <StripeConnectButton
                    isConnected={!!profile.stripe_account_id}
                    onboardingComplete={profile.stripe_onboarding_complete || false}
                  />
                </div>
                {!profile.stripe_account_id && (
                  <p className="text-xs text-gray-500 mt-1">
                    Connect Stripe to receive payments from subscribers
                  </p>
                )}
              </div>

              <div>
                <label className="text-xs sm:text-sm font-medium text-gray-500">Your Profile URL</label>
                <div className="mt-1">
                  <code className="text-xs sm:text-sm bg-gray-100 px-2 py-1 rounded text-gray-800 break-all">
                    onlydiary.app/{profile.custom_url || `u/${profile.id}`}
                  </code>
                </div>
                <Link
                  href={profile.custom_url ? `/${profile.custom_url}` : `/u/${profile.id}`}
                  className="block text-blue-600 hover:text-blue-800 text-xs sm:text-sm mt-1"
                >
                  View your public profile →
                </Link>
                {!profile.custom_url && (
                  <Link
                    href="/profile/edit"
                    className="block text-gray-500 hover:text-gray-700 text-xs mt-1"
                  >
                    Set custom URL →
                  </Link>
                )}
              </div>

              {profile.bio && (
                <div>
                  <label className="text-xs sm:text-sm font-medium text-gray-500">Bio</label>
                  <p className="text-gray-700 text-xs sm:text-sm leading-relaxed">{profile.bio}</p>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg p-4 sm:p-5 shadow-sm border border-gray-100">
            <h2 className="text-lg font-serif text-gray-800 mb-4">Quick Actions</h2>

            <div className="space-y-3">
              <Link
                href="/write"
                className="block w-full bg-gray-900 text-white text-center py-3 px-4 rounded-lg font-medium hover:bg-gray-800 transition-colors"
              >
                Write
              </Link>

              <Link
                href="/dashboard/notifications"
                className="block w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white text-center py-3 px-4 rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 transition-all duration-300"
              >
                🔔 Waitlist Notifications
              </Link>

              <div className="grid grid-cols-2 gap-2">
                <Link
                  href="/profile"
                  className="block w-full bg-gray-100 text-gray-700 text-center py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
                >
                  Edit Profile
                </Link>

                <Link
                  href={`/u/${profile.id}`}
                  className="block w-full bg-gray-100 text-gray-700 text-center py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
                >
                  View Profile
                </Link>
              </div>

              {/* Invite Friends */}
              <div className="mt-4">
                <InvitePrompt variant="card" userName={profile.name} />
              </div>
            </div>
          </div>

          {/* Promotions Section */}
          <div className="bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 rounded-lg p-4 sm:p-5 border border-purple-100">
            <h2 className="text-lg font-serif text-gray-800 mb-3">💰 Boost Your Revenue</h2>
            <p className="text-gray-600 text-sm mb-4">
              Turn your waitlists into instant sales with push notifications
            </p>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-lg">🔔</span>
                </div>
                <div>
                  <div className="font-medium text-gray-800">Waitlist Notifications</div>
                  <div className="text-sm text-gray-600">$0.05 per notification • 1-999 credits</div>
                </div>
              </div>

              <Link
                href="/dashboard/notifications"
                className="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors"
              >
                Learn More
              </Link>
            </div>
          </div>

          {/* Withdrawal Section */}
          <div id="withdrawal-section">
            <WithdrawalSection
              availableBalance={availableBalance}
              userId={user.id}
            />
          </div>
        </div>

        {/* Manage Entries */}
        <div className="mt-6 sm:mt-8">
          <div className="bg-white rounded-lg p-4 sm:p-5 shadow-sm border border-gray-100">
            <h2 className="text-lg font-serif text-gray-800 mb-4">Manage Entries</h2>
          
          <EntriesManager initialEntries={entries || []} />
          </div>
        </div>
      </div>
    </div>
  )
}
