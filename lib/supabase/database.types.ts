export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      flowers: {
        Row: {
          id: string
          receiver_id: string
          giver_id: string
          message: string
          created_at: string
        }
        Insert: {
          id?: string
          receiver_id: string
          giver_id: string
          message: string
          created_at?: string
        }
        Update: {
          id?: string
          receiver_id?: string
          giver_id?: string
          message?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "flowers_giver_id_fkey"
            columns: ["giver_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flowers_receiver_id_fkey"
            columns: ["receiver_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      subscriber_favorites: {
        Row: {
          id: string
          subscriber_id: string
          writer_id: string
          created_at: string
        }
        Insert: {
          id?: string
          subscriber_id: string
          writer_id: string
          created_at?: string
        }
        Update: {
          id?: string
          subscriber_id?: string
          writer_id?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriber_favorites_subscriber_id_fkey"
            columns: ["subscriber_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriber_favorites_writer_id_fkey"
            columns: ["writer_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          id: string
          email: string
          role: 'visitor' | 'subscriber' | 'writer' | 'admin'
          name: string | null
          avatar: string | null
          bio: string | null
          price_monthly: number | null
          stripe_account_id: string | null
          flower_count: number
          profile_picture_url: string | null
          social_twitter: string | null
          social_instagram: string | null
          social_website: string | null
          custom_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          role?: 'visitor' | 'subscriber' | 'writer' | 'admin'
          name?: string | null
          avatar?: string | null
          bio?: string | null
          price_monthly?: number | null
          stripe_account_id?: string | null
          flower_count?: number
          profile_picture_url?: string | null
          social_twitter?: string | null
          social_instagram?: string | null
          social_website?: string | null
          custom_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          role?: 'visitor' | 'subscriber' | 'writer' | 'admin'
          name?: string | null
          avatar?: string | null
          bio?: string | null
          price_monthly?: number | null
          stripe_account_id?: string | null
          flower_count?: number
          profile_picture_url?: string | null
          social_twitter?: string | null
          social_instagram?: string | null
          social_website?: string | null
          custom_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      favorite_creators: {
        Row: {
          id: string
          created_at: string
          writer_id: string
          user_id: string
        }
        Insert: {
          id?: string
          created_at?: string
          writer_id: string
          user_id: string
        }
        Update: {
          id?: string
          created_at?: string
          writer_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "favorite_creators_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "favorite_creators_writer_id_fkey"
            columns: ["writer_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      update_flower_count: {
        Args: Record<PropertyKey, never>
        Returns: unknown
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
