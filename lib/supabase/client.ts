import { createBrowserClient, createServerClient } from '@supabase/ssr'

// Database types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          role: 'visitor' | 'subscriber' | 'writer' | 'admin'
          name: string | null
          avatar: string | null
          bio: string | null
          price_monthly: number | null
          stripe_account_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          role?: 'visitor' | 'subscriber' | 'writer' | 'admin'
          name?: string | null
          avatar?: string | null
          bio?: string | null
          price_monthly?: number | null
          stripe_account_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          role?: 'visitor' | 'subscriber' | 'writer' | 'admin'
          name?: string | null
          avatar?: string | null
          bio?: string | null
          price_monthly?: number | null
          stripe_account_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      diary_entries: {
        Row: {
          id: string
          user_id: string
          title: string
          body_md: string
          is_free: boolean
          is_hidden: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          body_md: string
          is_free?: boolean
          is_hidden?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          body_md?: string
          is_free?: boolean
          is_hidden?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      photos: {
        Row: {
          id: string
          diary_entry_id: string
          url: string
          alt_text: string
          moderation_status: 'pending' | 'approved' | 'flagged' | 'rejected'
          rekognition_labels: Record<string, unknown> | null
          created_at: string
        }
        Insert: {
          id?: string
          diary_entry_id: string
          url: string
          alt_text: string
          moderation_status?: 'pending' | 'approved' | 'flagged' | 'rejected'
          rekognition_labels?: Record<string, unknown> | null
          created_at?: string
        }
        Update: {
          id?: string
          diary_entry_id?: string
          url?: string
          alt_text?: string
          moderation_status?: 'pending' | 'approved' | 'flagged' | 'rejected'
          rekognition_labels?: Record<string, unknown> | null
          created_at?: string
        }
      }
      post_credits: {
        Row: {
          id: string
          user_id: string
          writer_id: string
          credits_remaining: number
          total_credits_purchased: number
          last_purchase_date: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          writer_id: string
          credits_remaining?: number
          total_credits_purchased?: number
          last_purchase_date?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          writer_id?: string
          credits_remaining?: number
          total_credits_purchased?: number
          last_purchase_date?: string
          created_at?: string
          updated_at?: string
        }
      }
      post_reads: {
        Row: {
          id: string
          user_id: string
          writer_id: string
          diary_entry_id: string
          read_at: string
        }
        Insert: {
          id?: string
          user_id: string
          writer_id: string
          diary_entry_id: string
          read_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          writer_id?: string
          diary_entry_id?: string
          read_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          payer_id: string | null
          writer_id: string
          kind: 'sub' | 'donation'
          amount_cents: number
          stripe_payment_id: string | null
          credits_purchased: number | null
          credits_used_for_entry_id: string | null
          created_at: string
        }
        Insert: {
          id?: string
          payer_id?: string | null
          writer_id: string
          kind: 'sub' | 'donation'
          amount_cents: number
          stripe_payment_id?: string | null
          credits_purchased?: number | null
          credits_used_for_entry_id?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          payer_id?: string | null
          writer_id?: string
          kind?: 'sub' | 'donation'
          amount_cents?: number
          stripe_payment_id?: string | null
          credits_purchased?: number | null
          credits_used_for_entry_id?: string | null
          created_at?: string
        }
      }
      comments: {
        Row: {
          id: string
          diary_entry_id: string
          user_id: string
          body: string
          is_deleted: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          diary_entry_id: string
          user_id: string
          body: string
          is_deleted?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          diary_entry_id?: string
          user_id?: string
          body?: string
          is_deleted?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      flags: {
        Row: {
          id: string
          diary_entry_id: string
          reporter_id: string | null
          reason: string
          resolved: boolean
          created_at: string
        }
        Insert: {
          id?: string
          diary_entry_id: string
          reporter_id?: string | null
          reason: string
          resolved?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          diary_entry_id?: string
          reporter_id?: string | null
          reason?: string
          resolved?: boolean
          created_at?: string
        }
      }
      settings: {
        Row: {
          id: string
          key: string
          value: Record<string, unknown>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          key: string
          value: Record<string, unknown>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          key?: string
          value?: Record<string, unknown>
          created_at?: string
          updated_at?: string
        }
      }
      withdrawals: {
        Row: {
          id: string
          writer_id: string
          amount_cents: number
          status: 'pending' | 'processing' | 'completed' | 'rejected'
          requested_at: string
          processed_at: string | null
          stripe_transfer_id: string | null
          admin_notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          writer_id: string
          amount_cents: number
          status?: 'pending' | 'processing' | 'completed' | 'rejected'
          requested_at?: string
          processed_at?: string | null
          stripe_transfer_id?: string | null
          admin_notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          writer_id?: string
          amount_cents?: number
          status?: 'pending' | 'processing' | 'completed' | 'rejected'
          requested_at?: string
          processed_at?: string | null
          stripe_transfer_id?: string | null
          admin_notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      donations: {
        Row: {
          id: string
          donor_id: string | null
          writer_id: string
          payment_id: string
          amount: number
          message: string | null
          is_anonymous: boolean
          created_at: string
        }
        Insert: {
          id?: string
          donor_id?: string | null
          writer_id: string
          payment_id: string
          amount: number
          message?: string | null
          is_anonymous?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          donor_id?: string | null
          writer_id?: string
          payment_id?: string
          amount?: number
          message?: string | null
          is_anonymous?: boolean
          created_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          cover_image_url: string | null
          genre: string | null
          is_private: boolean
          is_complete: boolean
          price_type: 'project' | 'chapters'
          price_amount: number | null
          total_chapters: number
          total_words: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          cover_image_url?: string | null
          genre?: string | null
          is_private?: boolean
          is_complete?: boolean
          price_type?: 'project' | 'chapters'
          price_amount?: number | null
          total_chapters?: number
          total_words?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          cover_image_url?: string | null
          genre?: string | null
          is_private?: boolean
          is_complete?: boolean
          price_type?: 'project' | 'chapters'
          price_amount?: number | null
          total_chapters?: number
          total_words?: number
          created_at?: string
          updated_at?: string
        }
      }
      chapters: {
        Row: {
          id: string
          project_id: string
          user_id: string
          title: string
          content: string
          chapter_number: number
          word_count: number
          is_published: boolean
          love_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          user_id: string
          title: string
          content?: string
          chapter_number: number
          word_count?: number
          is_published?: boolean
          love_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          user_id?: string
          title?: string
          content?: string
          chapter_number?: number
          word_count?: number
          is_published?: boolean
          love_count?: number
          created_at?: string
          updated_at?: string
        }
      }
      project_waitlist: {
        Row: {
          id: string
          project_id: string
          user_email: string
          user_name: string | null
          user_id: string | null
          notification_sent: boolean
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          user_email: string
          user_name?: string | null
          user_id?: string | null
          notification_sent?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          user_email?: string
          user_name?: string | null
          user_id?: string | null
          notification_sent?: boolean
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      user_has_active_subscription: {
        Args: {
          subscriber_uuid: string
          writer_uuid: string
        }
        Returns: boolean
      }
      get_entry_preview: {
        Args: {
          entry_id: string
          viewer_id?: string
        }
        Returns: {
          id: string
          title: string
          body_md: string
          is_free: boolean
          created_at: string
          writer_name: string
          writer_price: number
          can_read_full: boolean
        }[]
      }
      get_writer_public_data: {
        Args: {
          writer_uuid: string
        }
        Returns: {
          writer_id: string
          writer_name: string | null
          writer_avatar: string | null
          writer_bio: string | null
          writer_price_monthly: number | null
          free_entry_id: string | null
          free_entry_title: string | null
          free_entry_body_md: string | null
          free_entry_created_at: string | null
        }[]
      }
      get_writer_locked_entries: {
        Args: {
          writer_uuid: string
        }
        Returns: {
          entry_id: string
          title: string
          body_teaser: string
          created_at: string
        }[]
      }
    }
    Enums: {
      user_role: 'visitor' | 'subscriber' | 'writer' | 'admin'
      moderation_status: 'pending' | 'approved' | 'flagged' | 'rejected'
      payment_kind: 'sub' | 'donation'
      withdrawal_status: 'pending' | 'processing' | 'completed' | 'rejected'
    }
  }
}

// Enhanced retry configuration
interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffMultiplier: 2
}

// Network status tracking
let isOnline = true
let networkListenersAdded = false

const addNetworkListeners = () => {
  if (typeof window === 'undefined' || networkListenersAdded) return

  const updateOnlineStatus = () => {
    isOnline = navigator.onLine
    if (isOnline) {
      console.log('🌐 Connection restored')
    } else {
      console.log('📵 Connection lost')
    }
  }

  window.addEventListener('online', updateOnlineStatus)
  window.addEventListener('offline', updateOnlineStatus)
  networkListenersAdded = true
  isOnline = navigator.onLine
}

// Enhanced error handling
const getErrorMessage = (error: unknown): string => {
  const err = error as { message?: string; code?: string }
  if (!isOnline) {
    return 'No internet connection. Please check your network and try again.'
  }

  if (err?.message?.includes('Failed to fetch')) {
    return 'Connection issue. Retrying automatically...'
  }

  if (err?.message?.includes('JWT')) {
    return 'Session expired. Please refresh the page.'
  }

  if (err?.code === 'PGRST301') {
    return 'Database temporarily unavailable. Retrying...'
  }

  return err?.message || 'An unexpected error occurred'
}

// Exponential backoff delay
const getRetryDelay = (attempt: number, config: RetryConfig): number => {
  const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1)
  return Math.min(delay, config.maxDelay)
}

// Sleep utility
const sleep = (ms: number): Promise<void> =>
  new Promise(resolve => setTimeout(resolve, ms))

// Retry wrapper for any async function
const withRetry = async <T>(
  operation: () => Promise<T>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG,
  operationName: string = 'operation'
): Promise<T> => {
  let lastError: unknown

  for (let attempt = 1; attempt <= config.maxRetries + 1; attempt++) {
    try {
      // Check if we're online before attempting
      if (!isOnline && attempt > 1) {
        throw new Error('No internet connection')
      }

      const result = await operation()

      // Success - log if this was a retry
      if (attempt > 1) {
        console.log(`✅ ${operationName} succeeded on attempt ${attempt}`)
      }

      return result
    } catch (error) {
      lastError = error

      // Don't retry on certain errors
      if (error?.code === 'PGRST116' || // Row not found
          error?.code === 'PGRST204' || // No content
          error?.message?.includes('duplicate key') ||
          error?.message?.includes('violates check constraint')) {
        throw error
      }

      // If this was the last attempt, throw the error
      if (attempt > config.maxRetries) {
        console.error(`❌ ${operationName} failed after ${config.maxRetries} retries:`, error)
        throw error
      }

      // Calculate delay and wait
      const delay = getRetryDelay(attempt, config)
      console.warn(`⚠️ ${operationName} failed (attempt ${attempt}/${config.maxRetries}). Retrying in ${delay}ms...`, getErrorMessage(error))

      await sleep(delay)
    }
  }

  throw lastError
}

// Client-side Supabase client (for browser) - Enhanced but Simple
export const createSupabaseClient = () => {
  // Add network listeners
  addNetworkListeners()

  const client = createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce'
      },
      global: {
        headers: {
          'x-client-info': 'onlydiary-web'
        }
      }
    }
  )

  // Only enhance auth operations to avoid breaking database query chains
  // Database operations work fine with Supabase's built-in retry logic

  const originalGetUser = client.auth.getUser.bind(client.auth)
  const originalGetSession = client.auth.getSession.bind(client.auth)

  // Add retry logic only to the most critical auth operations
  client.auth.getUser = async () => {
    try {
      return await withRetry(
        () => originalGetUser(),
        { ...DEFAULT_RETRY_CONFIG, maxRetries: 2 },
        'auth.getUser'
      )
    } catch (error) {
      console.warn('Auth getUser retry failed, using original:', error)
      return originalGetUser()
    }
  }

  client.auth.getSession = async () => {
    try {
      return await withRetry(
        () => originalGetSession(),
        { ...DEFAULT_RETRY_CONFIG, maxRetries: 2 },
        'auth.getSession'
      )
    } catch (error) {
      console.warn('Auth getSession retry failed, using original:', error)
      return originalGetSession()
    }
  }

  return client
}

// Server-side Supabase client (for API routes and SSR)
export const createSupabaseServerClient = async () => {
  const { cookies } = await import('next/headers')
  const cookieStore = await cookies()
  
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: Record<string, unknown>) {
          try {
            cookieStore.set({ name, value, ...options })
          } catch (error) {
            // Ignore cookie errors in server components
            console.warn('Cookie set failed:', error)
          }
        },
        remove(name: string, options: Record<string, unknown>) {
          try {
            cookieStore.set({ name, value: '', ...options })
          } catch (error) {
            // Ignore cookie errors in server components
            console.warn('Cookie remove failed:', error)
          }
        },
      },
    }
  )
}