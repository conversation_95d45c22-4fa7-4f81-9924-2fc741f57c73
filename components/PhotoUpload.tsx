"use client"

import { useState, useRef, useEffect, useCallback } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { photoStorage } from "@/lib/supabase/storage"
import { addWatermarkToFiles, validateImageFile } from "@/lib/watermark"
import Image from "next/image"

interface Photo {
  id: string
  url: string
  alt_text: string
  moderation_status: 'pending' | 'approved' | 'flagged' | 'rejected'
  created_at: string
}

interface PhotoUploadProps {
  entryId?: string
  onPhotosChange?: (photos: Photo[]) => void
  onAutoSave?: () => Promise<string | null> // Returns entry ID after auto-save
  hasUnsavedChanges?: boolean
}

export function PhotoUpload({ entryId, onPhotosChange, onAutoSave, hasUnsavedChanges }: PhotoUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [loading, setLoading] = useState(false)
  const [photos, setPhotos] = useState<Photo[]>([])
  const [error, setError] = useState("")
  const [autoSaving, setAutoSaving] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: number}>({})
  const [uploadingFiles, setUploadingFiles] = useState<string[]>([])
  const [watermarking, setWatermarking] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const supabase = createSupabaseClient()

  const loadExistingPhotos = useCallback(async () => {
    if (!entryId) return

    setLoading(true)
    setError("")

    try {
      console.log('Loading photos for entry:', entryId)

      const { data: existingPhotos, error: loadError } = await supabase
        .from('photos')
        .select('*')
        .eq('diary_entry_id', entryId)
        .order('created_at', { ascending: true })

      if (loadError) {
        console.error('Error loading photos:', loadError)
        setError("Failed to load existing photos")
      } else {
        console.log('Loaded photos:', existingPhotos)
        setPhotos(existingPhotos || [])
        onPhotosChange?.(existingPhotos || [])
      }
    } catch (err) {
      console.error('Error loading photos:', err)
      setError("Failed to load existing photos")
    } finally {
      setLoading(false)
    }
  }, [entryId, supabase, onPhotosChange])

  // Load existing photos when entryId changes
  useEffect(() => {
    if (entryId) {
      loadExistingPhotos()
    } else {
      setPhotos([])
    }
  }, [entryId, loadExistingPhotos])



  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    if (photos.length + files.length > 20) {
      setError("Maximum 20 photos per entry")
      return
    }

    setUploading(true)
    setError("")

    try {
      // Step 1: Validate and watermark images
      const fileArray = Array.from(files)

      // Validate all files first
      for (const file of fileArray) {
        const validation = validateImageFile(file)
        if (!validation.valid) {
          throw new Error(validation.error)
        }
      }

      // Add watermarks to all images
      setWatermarking(true)
      setError("Adding watermarks...")

      const watermarkedFiles = await addWatermarkToFiles(fileArray, {
        text: 'www.OnlyDiary.app',
        position: 'bottom-right',
        opacity: 0.7,
        fontSize: 16
      })

      setWatermarking(false)
      setError("")

      // Step 2: Auto-save entry if needed
      let currentEntryId: string | undefined = entryId

      if (!currentEntryId && onAutoSave) {
        setAutoSaving(true)
        setError("Saving entry...")
        const savedEntryId = await onAutoSave()
        setAutoSaving(false)

        if (!savedEntryId) {
          throw new Error("Please add some content to your entry before uploading photos")
        }
        currentEntryId = savedEntryId
      } else if (!currentEntryId) {
        throw new Error("Entry must be saved before uploading photos")
      }

      // Step 3: Upload watermarked photos to the saved entry with timeout and progress
      const fileNames = watermarkedFiles.map(file => file.name)
      setUploadingFiles(fileNames)

      const uploadPromises = watermarkedFiles.map(async (file) => {
        // Validate file type
        if (!file.type.startsWith('image/')) {
          throw new Error(`${file.name} is not an image file`)
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          throw new Error(`${file.name} is too large (max 10MB)`)
        }

        // Create unique filename
        const fileExt = file.name.split('.').pop()
        const fileName = `${currentEntryId}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`

        // Set initial progress
        setUploadProgress(prev => ({ ...prev, [file.name]: 0 }))

        // Upload with timeout (30 seconds)
        const uploadPromise = photoStorage.upload(fileName, file)
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error(`Upload timeout for ${file.name}`)), 30000)
        )

        try {
          const { error: uploadError } = await Promise.race([uploadPromise, timeoutPromise]) as any

          if (uploadError) {
            throw new Error(`Failed to upload ${file.name}: ${uploadError.message}`)
          }

          // Update progress to 100%
          setUploadProgress(prev => ({ ...prev, [file.name]: 100 }))
        } catch (error: any) {
          // Remove from progress on error
          setUploadProgress(prev => {
            const newProgress = { ...prev }
            delete newProgress[file.name]
            return newProgress
          })
          throw error
        }

        // Get public URL
        const { data: { publicUrl } } = photoStorage.getPublicUrl(fileName)

        // Save photo as APPROVED immediately - user sees it right away
        const { data: photoData, error: photoError } = await supabase
          .from('photos')
          .insert({
            diary_entry_id: currentEntryId,
            url: publicUrl,
            alt_text: file.name,
            moderation_status: 'approved'
          })
          .select()
          .single()

        if (photoError) {
          throw new Error(`Failed to save photo record: ${photoError.message}`)
        }

        // AWS moderation runs in background - doesn't affect user experience
        fetch('/api/moderate-photo', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ photoId: photoData.id })
        }).catch(() => {
          // Ignore errors - photo is already approved and visible
        })

        return photoData
      })

      const uploadedPhotos = await Promise.all(uploadPromises)
      const newPhotos = [...photos, ...uploadedPhotos]
      setPhotos(newPhotos)
      onPhotosChange?.(newPhotos)

    } catch (err: any) {
      setError(err.message || "Failed to upload photos")
    } finally {
      setUploading(false)
      setWatermarking(false)
      setUploadingFiles([])
      setUploadProgress({})
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleRemovePhoto = async (photoId: string) => {
    try {
      const { error } = await supabase
        .from('photos')
        .delete()
        .eq('id', photoId)

      if (error) {
        setError("Failed to remove photo")
        return
      }

      const newPhotos = photos.filter(photo => photo.id !== photoId)
      setPhotos(newPhotos)
      onPhotosChange?.(newPhotos)
    } catch {
      setError("Failed to remove photo")
    }
  }

  return (
    <div className="border-t border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Photos</h3>
        <span className="text-sm text-gray-500">{photos.length}/20</span>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {autoSaving && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-600 text-sm">💾 Auto-saving your entry...</p>
        </div>
      )}

      {watermarking && (
        <div className="mb-4 p-3 bg-purple-50 border border-purple-200 rounded-lg">
          <p className="text-purple-600 text-sm">🎨 Adding watermarks to your photos...</p>
        </div>
      )}

      {/* Loading indicator */}
      {loading && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-600 text-sm">Loading photos...</p>
        </div>
      )}

      {/* Upload Progress */}
      {uploadingFiles.length > 0 && (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-600 text-sm font-medium mb-3">
            📤 Uploading {uploadingFiles.length} photo{uploadingFiles.length > 1 ? 's' : ''}...
          </p>
          <div className="space-y-2">
            {uploadingFiles.map((fileName) => (
              <div key={fileName} className="flex items-center gap-3">
                <div className="flex-1">
                  <div className="flex justify-between text-xs text-blue-600 mb-1">
                    <span className="truncate max-w-[200px]">{fileName}</span>
                    <span>{uploadProgress[fileName] || 0}%</span>
                  </div>
                  <div className="w-full bg-blue-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress[fileName] || 0}%` }}
                    ></div>
                  </div>
                </div>
                {uploadProgress[fileName] === 100 && (
                  <span className="text-green-600 text-xs">✓</span>
                )}
              </div>
            ))}
          </div>
          <p className="text-xs text-blue-500 mt-2">
            ⏱️ This may take up to 30 seconds per photo
          </p>
        </div>
      )}

      {/* Upload Button */}
      <div className="mb-4">
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={uploading || loading || watermarking || photos.length >= 20}
          className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {autoSaving ? "Saving entry..." : watermarking ? "Adding watermarks..." : uploading ? "Uploading..." : "Add Photos"}
        </button>
        {!entryId && !onAutoSave && (
          <p className="text-xs text-gray-500 mt-1">
            Save your entry first to upload photos
          </p>
        )}
        {!entryId && onAutoSave && (
          <p className="text-xs text-gray-500 mt-1">
            Your entry will be auto-saved when you add photos
          </p>
        )}
      </div>

      {/* Photo Grid - Facebook style thumbnails */}
      {photos.length > 0 && (
        <div className="space-y-4">
          {photos.map((photo) => (
            <div key={photo.id} className="relative group border border-gray-200 rounded-lg overflow-hidden">
              <Image
                src={photo.url}
                alt={photo.alt_text}
                width={400}
                height={320}
                className="w-full max-h-80 object-contain bg-gray-50"
              />
              <button
                onClick={() => handleRemovePhoto(photo.id)}
                className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
              >
                ×
              </button>
              {photo.moderation_status === 'flagged' && (
                <div className="absolute bottom-2 left-2 bg-orange-500 text-white text-xs px-2 py-1 rounded">
                  Flagged for Review
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
